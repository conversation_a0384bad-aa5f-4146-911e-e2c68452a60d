<?php
if (!defined('ABSPATH')) exit;

// Load child theme styles
function chld_thm_cfg_locale_css($uri) {
    if (empty($uri) && is_rtl() && file_exists(get_template_directory() . '/rtl.css'))
        $uri = get_template_directory_uri() . '/rtl.css';
    return $uri;
}
add_filter('locale_stylesheet_uri', 'chld_thm_cfg_locale_css');

function child_theme_configurator_css() {
    wp_enqueue_style('chld_thm_cfg_child', trailingslashit(get_stylesheet_directory_uri()) . 'style.css', array('astra-theme-css','woocommerce-layout','woocommerce-smallscreen','woocommerce-general'));
}
add_action('wp_enqueue_scripts', 'child_theme_configurator_css', 10);

// Enqueue custom FPD JS
function astra_child_enqueue_fpd_scripts() {
    if (is_product()) {
        $file_path = get_stylesheet_directory() . '/js/custom-fpd20.js';
        $version = file_exists($file_path) ? filemtime($file_path) : '1.0.0';
        wp_enqueue_script(
            'custom-fpd-js',
            get_stylesheet_directory_uri() . '/js/custom-fpd20.js',
            array('jquery'),
            $version,
            true
        );
        wp_localize_script(
            'custom-fpd-js',
            'fpdCustomData',
            array(
                'goldenLeather' => home_url('/wp-content/uploads/fpd_patterns_text/background-golden.png'),
                'silverLeather' => home_url('/wp-content/uploads/fpd_patterns_text/background-silver.png'),
            )
        );
    }
}
add_action('wp_enqueue_scripts', 'astra_child_enqueue_fpd_scripts');

// Add hidden input for background image data
// add_action('woocommerce_before_add_to_cart_button', function() {
//     echo '<input type="hidden" name="background_image_data" value="">';
// });

// Capture FPD + background image data when added to cart
// add_filter('woocommerce_add_cart_item_data', function($cart_item_data, $product_id, $variation_id) {
//     if (isset($_POST['fpd_data'])) {
//         $fpd_data = json_decode(stripslashes($_POST['fpd_data']), true);
//         $cart_item_data['fpd_data'] = $fpd_data;
//     }
//     if (isset($_POST['background_image_data'])) {
//         $cart_item_data['background_image_data'] = json_decode(stripslashes($_POST['background_image_data']), true);
//     }
//     return $cart_item_data;
// }, 10, 3);

// Modify FPD order data with background image for text elements
// add_filter('fpd_order_data', function($fpd_data) {
//     if (isset($_POST['background_image_data'])) {
//         $background_images = json_decode(stripslashes($_POST['background_image_data']), true);
//         if (is_array($background_images)) {
//             foreach ($fpd_data['product'] as &$view) {
//                 if (isset($view['elements'])) {
//                     foreach ($view['elements'] as &$element) {
//                         if ($element['type'] === 'text') {
//                             foreach ($background_images as $bg) {
//                                 if (abs($element['left'] - $bg['left']) < 5 && abs($element['top'] - $bg['top']) < 5) {
//                                     $element['parameters']['backgroundImage'] = $bg['imageUrl'];
//                                     $element['backgroundImageUrl'] = $bg['imageUrl'];
//                                     break;
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     }
//     return $fpd_data;
// }, 10);

// // Save FPD thumbnail in order meta
// add_action('woocommerce_checkout_create_order_line_item', function($item, $cart_item_key, $values, $order) {
//     if (isset($values['_fpd_product_thumbnail'])) {
//         $item->add_meta_data('_fpd_product_thumbnail', $values['_fpd_product_thumbnail']);
//     }
// }, 10, 4);

// // Display thumbnail in admin
// add_action('woocommerce_after_order_itemmeta', function($item_id, $item, $product) {
//     $thumbnail = wc_get_order_item_meta($item_id, '_fpd_product_thumbnail', true);
//     if ($thumbnail && strpos($thumbnail, 'data:image') === 0) {
//         echo '<p><strong>Customization Preview:</strong><br><img src="' . esc_attr($thumbnail) . '" style="max-width:200px; border:1px solid #ddd;"></p>';
//     }
// }, 10, 3);

// Save background image to order meta (optional, for admin display)
// add_action('woocommerce_checkout_create_order_line_item', function($item, $cart_item_key, $values) {
//     if (isset($values['fpd_data'])) {
//         $fpd_data = $values['fpd_data'];
//         foreach ($fpd_data['product'] as $view) {
//             foreach ($view['elements'] as $element) {
//                 if ($element['type'] === 'text' && isset($element['parameters']['backgroundImage'])) {
//                     $item->add_meta_data('_text_background', esc_url_raw($element['parameters']['backgroundImage']));
//                 }
//             }
//         }
//     }
// }, 10, 3);

// // Show background image in admin order details
// add_action('woocommerce_order_item_meta_end', function($item_id, $item) {
//     $background = $item->get_meta('_text_background');
//     if ($background) {
//         echo '<div class="text-bg-preview"><strong>Text Background:</strong><br>';
//         echo '<img src="' . esc_url($background) . '" style="max-width: 150px;"></div>';
//     }
// }, 10, 2);

// Add class to shop page

add_filter('body_class', 'custom_shop_body_class');
function custom_shop_body_class($classes) {
    if (is_post_type_archive('product')) {
        $classes[] = 'is-shop-page';
    }
    return $classes;
};


// Fix for FPD customized product thumbnail in emails
function ensure_fpd_thumbnail_setting_enabled() {
    // Check if FPD plugin is active and the setting exists
    if (function_exists('fpd_get_option')) {
        $current_setting = get_option('fpd_order_product_thumbnail', 'yes');

        // Enable the setting if it's not already enabled
        if ($current_setting !== 'yes') {
            update_option('fpd_order_product_thumbnail', 'yes');
            error_log('FPD: Enabled customized product thumbnail setting for emails');
        }
    }
}
add_action('init', 'ensure_fpd_thumbnail_setting_enabled');

// Add debugging for FPD email thumbnails
function debug_fpd_email_thumbnail($item_id, $item, $order, $plain_text = false) {
    if (!$plain_text && function_exists('fpd_get_option')) {
        $setting_enabled = fpd_get_option('fpd_order_product_thumbnail');
        $has_thumbnail = isset($item['_fpd_product_thumbnail']);
        $thumbnail_data = $has_thumbnail ? substr($item['_fpd_product_thumbnail'], 0, 50) . '...' : 'none';

        error_log("FPD Email Debug - Item ID: $item_id, Setting Enabled: $setting_enabled, Has Thumbnail: " . ($has_thumbnail ? 'yes' : 'no') . ", Thumbnail Data: $thumbnail_data");
    }
}
add_action('woocommerce_order_item_meta_start', 'debug_fpd_email_thumbnail', 5, 4);

// Ensure FPD thumbnail data is properly saved to order items
function ensure_fpd_thumbnail_in_order($item, $cart_item_key, $values, $order) {
    // Check if this cart item has FPD data with thumbnail
    if (isset($values['fpd_data']) && isset($values['fpd_data']['fpd_product_thumbnail'])) {
        $thumbnail = $values['fpd_data']['fpd_product_thumbnail'];

        // Save the thumbnail to order item meta
        $item->add_meta_data('_fpd_product_thumbnail', $thumbnail, true);

        error_log("FPD: Saved thumbnail to order item meta for item: " . $item->get_name());
    }
}
add_action('woocommerce_checkout_create_order_line_item', 'ensure_fpd_thumbnail_in_order', 10, 4);

// Fix for account orders page thumbnail display
function fix_fpd_account_orders_thumbnail($item_id, $item, $order, $plain_text = false) {
    if (!$plain_text && function_exists('fpd_get_option') && fpd_get_option('fpd_order_product_thumbnail')) {
        $thumbnail = wc_get_order_item_meta($item_id, '_fpd_product_thumbnail', true);

        if (!empty($thumbnail) && strpos($thumbnail, 'data:image') === 0) {
            echo '<div style="margin: 10px 0;"><strong>Customization Preview:</strong><br>';
            echo '<img src="' . esc_attr($thumbnail) . '" style="max-width: 150px; height: auto; border: 1px solid #ddd; border-radius: 4px;" alt="Customized Product Preview" /></div>';
        }
    }
}
add_action('woocommerce_order_item_meta_end', 'fix_fpd_account_orders_thumbnail', 10, 4);

// Force FPD thumbnail display in emails with higher priority
function force_fpd_email_thumbnail_display($item_id, $item, $order, $plain_text = false) {
    if (!$plain_text) {
        $thumbnail = wc_get_order_item_meta($item_id, '_fpd_product_thumbnail', true);

        if (!empty($thumbnail)) {
            error_log("FPD Force Display: Found thumbnail for item $item_id");

            if (strpos($thumbnail, 'data:image') === 0) {
                // Display the thumbnail using inline base64
                echo '<div style="clear: both; margin: 10px 0; padding: 10px; border: 1px solid #ddd; background: #f9f9f9;">';
                echo '<strong style="display: block; margin-bottom: 5px;">Customized Product Preview:</strong>';
                echo '<img src="' . esc_attr($thumbnail) . '" style="max-width: 120px; height: auto; border: 1px solid #ccc;" alt="Customized Product" />';
                echo '</div>';
            }
        } else {
            error_log("FPD Force Display: No thumbnail found for item $item_id");
        }
    }
}
// Use a higher priority to ensure it runs after FPD's own function
add_action('woocommerce_order_item_meta_start', 'force_fpd_email_thumbnail_display', 15, 4);

//